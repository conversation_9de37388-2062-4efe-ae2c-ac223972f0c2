import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

interface CartItem {
  id: number
  title: string
  price: number
  quantity: number
  image: string
  skuId: string
}

export const useCartStore = defineStore('cart', () => {
  // State
  const items = ref<CartItem[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)

  // Computed
  const itemCount = computed(() => items.value.reduce((sum, item) => sum + item.quantity, 0))
  const totalPrice = computed(() => {
    return items.value.reduce((sum, item) => sum + item.price * item.quantity, 0)
  })

  // Actions
  function addToCart(item: Partial<CartItem>) {
    try {
      loading.value = true
      error.value = null

      // 输入验证
      if (!item.id || !item.title || !item.price) {
        throw new Error('商品信息不完整')
      }

      // 更新购物车
      const existingItem = items.value.find(i => i.id === item.id)
      if (existingItem) {
        existingItem.quantity += item.quantity || 1
      } else {
        items.value.push({
          id: item.id,
          title: item.title,
          price: item.price,
          quantity: item.quantity || 1,
          image: item.image || '',
          skuId: item.skuId || ''
        })
      }

      // 持久化存储
      saveToLocalStorage()
      return true
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : '添加购物车失败'
      error.value = errorMessage
      return false
    } finally {
      loading.value = false
    }
  }

  function removeFromCart(itemId: number) {
    try {
      loading.value = true
      error.value = null

      items.value = items.value.filter(item => item.id !== itemId)
      saveToLocalStorage()
      return true
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : '移除商品失败'
      error.value = errorMessage
      return false
    } finally {
      loading.value = false
    }
  }

  function updateQuantity(itemId: number, quantity: number) {
    try {
      loading.value = true
      error.value = null

      // 输入验证
      if (quantity < 1) {
        throw new Error('商品数量不能小于1')
      }

      const item = items.value.find(i => i.id === itemId)
      if (item) {
        item.quantity = quantity
        saveToLocalStorage()
      }

      return true
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : '更新数量失败'
      error.value = errorMessage
      return false
    } finally {
      loading.value = false
    }
  }

  function clearCart() {
    items.value = []
    clearLocalStorage()
  }

  // 持久化相关
  function saveToLocalStorage() {
    try {
      localStorage.setItem('cart_items', JSON.stringify(items.value))
    } catch (e) {
      console.error('Failed to save cart data:', e)
    }
  }

  function loadFromLocalStorage() {
    try {
      const savedItems = localStorage.getItem('cart_items')
      if (savedItems) {
        items.value = JSON.parse(savedItems)
      }
    } catch (e) {
      console.error('Failed to load cart data from localStorage:', e)
      clearLocalStorage()
    }
  }

  function clearLocalStorage() {
    try {
      localStorage.removeItem('cart_items')
    } catch (e) {
      console.error('Failed to clear cart data:', e)
    }
  }

  // 初始化时加载购物车数据
  loadFromLocalStorage()

  return {
    // State
    items,
    loading,
    error,
    
    // Computed
    itemCount,
    totalPrice,
    
    // Actions
    addToCart,
    removeFromCart,
    updateQuantity,
    clearCart
  }
})