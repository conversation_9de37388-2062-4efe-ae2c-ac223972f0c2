<template>
  <div class="test-page">
    <h1>测试页面</h1>
    <p>如果你能看到这个页面，说明 Vue 应用正在正常运行！</p>
    
    <div class="test-section">
      <h2>Element Plus 测试</h2>
      <el-button type="primary">主要按钮</el-button>
      <el-button type="success">成功按钮</el-button>
      <el-button type="warning">警告按钮</el-button>
      <el-button type="danger">危险按钮</el-button>
    </div>
    
    <div class="test-section">
      <h2>路由测试</h2>
      <router-link to="/">返回首页</router-link>
    </div>
    
    <div class="test-section">
      <h2>响应式数据测试</h2>
      <p>计数器: {{ count }}</p>
      <el-button @click="count++">增加</el-button>
      <el-button @click="count--">减少</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const count = ref(0)
</script>

<style scoped>
.test-page {
  padding: 40px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

h1 {
  color: #e3393c;
  text-align: center;
}

h2 {
  color: #333;
  margin-bottom: 15px;
}

.el-button {
  margin-right: 10px;
}
</style>
