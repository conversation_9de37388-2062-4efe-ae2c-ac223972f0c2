<template>
  <div class="home">
    <div class="simple-header">
      <h1>京东首页</h1>
      <p>欢迎来到京东商城</p>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 测试区域 -->
        <section class="test-section">
          <h2>基础功能测试</h2>
          <el-button type="primary" @click="testClick">点击测试</el-button>
          <p v-if="message">{{ message }}</p>
        </section>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const message = ref('')

const testClick = () => {
  message.value = '按钮点击成功！Vue 应用正常工作！'
  console.log('测试点击成功')
}
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: #f5f5f5;
}

.simple-header {
  background: #e3393c;
  color: white;
  text-align: center;
  padding: 40px 20px;
}

.simple-header h1 {
  font-size: 32px;
  margin: 0 0 10px 0;
}

.simple-header p {
  font-size: 16px;
  margin: 0;
  opacity: 0.9;
}

.main-content {
  padding: 40px 20px;
}

.container {
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  background: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.test-section h2 {
  color: #333;
  margin-bottom: 20px;
}

.test-section p {
  margin-top: 15px;
  color: #666;
  font-size: 16px;
}
</style>