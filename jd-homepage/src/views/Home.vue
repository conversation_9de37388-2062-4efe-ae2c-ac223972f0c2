<template>
  <div class="home">
    <!-- 顶部导航 -->
    <TopNav />

    <!-- 主导航 -->
    <MainNav />

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="container">
        <!-- 首页横幅区域 -->
        <section class="hero-section">
          <div class="hero-content">
            <!-- 左侧分类菜单 -->
            <aside class="category-sidebar">
              <CategoryMenu :isVisible="true" />
            </aside>

            <!-- 中间轮播图 -->
            <div class="carousel-area">
              <Carousel />
            </div>

            <!-- 右侧快捷入口 -->
            <aside class="shortcuts-sidebar">
              <ShortcutMenu />
            </aside>
          </div>
        </section>
        
        <!-- 秒杀区域 -->
        <section class="seckill-section">
          <div class="section-header">
            <h2 class="section-title">
              <i class="seckill-icon"></i>
              京东秒杀
            </h2>
            <div class="seckill-timer">
              <span class="timer-label">距离结束</span>
              <div class="timer-display">
                <span class="timer-item">{{ seckillTime.hours }}</span>
                <span class="timer-separator">:</span>
                <span class="timer-item">{{ seckillTime.minutes }}</span>
                <span class="timer-separator">:</span>
                <span class="timer-item">{{ seckillTime.seconds }}</span>
              </div>
            </div>
            <a href="/seckill" class="more-link">更多秒杀</a>
          </div>
          <div class="seckill-products">
            <div 
              v-for="product in seckillProducts" 
              :key="product.id"
              class="seckill-product"
              @click="viewProduct(product)"
            >
              <div class="product-image">
                <img :src="product.image" :alt="product.name" />
                <div class="product-badge" v-if="product.badge">{{ product.badge }}</div>
              </div>
              <div class="product-info">
                <h3 class="product-name">{{ product.name }}</h3>
                <div class="product-price">
                  <span class="price-current">¥{{ product.seckillPrice }}</span>
                  <span class="price-original">¥{{ product.originalPrice }}</span>
                </div>
                <div class="product-progress">
                  <div class="progress-bar">
                    <div class="progress-fill" :style="{ width: product.progress + '%' }"></div>
                  </div>
                  <span class="progress-text">已抢{{ product.progress }}%</span>
                </div>
              </div>
            </div>
          </div>
        </section>
        
        <!-- 推荐区域 -->
        <section class="recommend-section">
          <div class="section-header">
            <h2 class="section-title">为你推荐</h2>
            <div class="recommend-tabs">
              <button 
                v-for="tab in recommendTabs" 
                :key="tab.id"
                class="tab-btn"
                :class="{ active: currentTab === tab.id }"
                @click="switchTab(tab.id)"
              >
                {{ tab.name }}
              </button>
            </div>
            <a href="/recommend" class="more-link">查看更多</a>
          </div>
          <div class="recommend-products">
            <div 
              v-for="product in recommendProducts" 
              :key="product.id"
              class="recommend-product"
              @click="viewProduct(product)"
            >
              <div class="product-image">
                <img :src="product.image" :alt="product.name" />
              </div>
              <div class="product-info">
                <h3 class="product-name">{{ product.name }}</h3>
                <div class="product-price">
                  <span class="price-current">¥{{ product.price }}</span>
                  <span class="price-tag" v-if="product.tag">{{ product.tag }}</span>
                </div>
                <div class="product-rating" v-if="product.rating">
                  <div class="stars">
                    <i v-for="i in 5" :key="i" class="star" :class="{ filled: i <= product.rating }"></i>
                  </div>
                  <span class="rating-text">({{ product.reviewCount }})</span>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>
    </main>
    
    <!-- 底部信息 -->
    <footer class="footer">
      <div class="container">
        <div class="footer-content">
          <div class="footer-section">
            <h4>购物指南</h4>
            <ul>
              <li><a href="#">购物流程</a></li>
              <li><a href="#">会员介绍</a></li>
              <li><a href="#">生活旅行</a></li>
              <li><a href="#">常见问题</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>配送方式</h4>
            <ul>
              <li><a href="#">上门自提</a></li>
              <li><a href="#">211限时达</a></li>
              <li><a href="#">配送服务查询</a></li>
              <li><a href="#">配送费收取标准</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>支付方式</h4>
            <ul>
              <li><a href="#">货到付款</a></li>
              <li><a href="#">在线支付</a></li>
              <li><a href="#">分期付款</a></li>
              <li><a href="#">京东钱包</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>售后服务</h4>
            <ul>
              <li><a href="#">售后政策</a></li>
              <li><a href="#">价格保护</a></li>
              <li><a href="#">退款说明</a></li>
              <li><a href="#">返修/退换货</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4>特色服务</h4>
            <ul>
              <li><a href="#">夺宝岛</a></li>
              <li><a href="#">DIY装机</a></li>
              <li><a href="#">延保服务</a></li>
              <li><a href="#">京东E卡</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p>&copy; 2024 JD.com 版权所有</p>
          <p>京公网安备 11000002000088号 | 京ICP备11041704号</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

// 逐步恢复组件
import TopNav from '@/components/nav/TopNav.vue'
import MainNav from '@/components/nav/MainNav.vue'
import CategoryMenu from '@/components/menu/CategoryMenu.vue'
import Carousel from '@/components/carousel/Carousel.vue'
import ShortcutMenu from '@/components/shortcuts/ShortcutMenu.vue'

interface Product {
  id: string
  name: string
  image: string
  price?: number
  originalPrice?: number
  seckillPrice?: number
  badge?: string
  tag?: string
  progress?: number
  rating?: number
  reviewCount?: number
}

interface RecommendTab {
  id: string
  name: string
}

const router = useRouter()

// 秒杀倒计时
const seckillTime = ref({
  hours: '02',
  minutes: '35',
  seconds: '48'
})

// 推荐标签
const recommendTabs = ref<RecommendTab[]>([
  { id: 'recommend', name: '猜你喜欢' },
  { id: 'hot', name: '热卖商品' },
  { id: 'new', name: '新品首发' },
  { id: 'brand', name: '品牌精选' }
])

const currentTab = ref('recommend')

// 秒杀商品数据
const seckillProducts = ref<Product[]>([
  {
    id: 'sk1',
    name: 'iPhone 15 Pro Max 256GB',
    image: '/images/products/iphone15.jpg',
    originalPrice: 9999,
    seckillPrice: 8999,
    progress: 78,
    badge: '限量'
  },
  {
    id: 'sk2',
    name: '小米14 Ultra 12GB+256GB',
    image: '/images/products/mi14.jpg',
    originalPrice: 5999,
    seckillPrice: 4999,
    progress: 45
  },
  {
    id: 'sk3',
    name: 'MacBook Pro M3 14英寸',
    image: '/images/products/macbook.jpg',
    originalPrice: 15999,
    seckillPrice: 13999,
    progress: 62
  },
  {
    id: 'sk4',
    name: 'iPad Air 11英寸 M2芯片',
    image: '/images/products/ipad.jpg',
    originalPrice: 4999,
    seckillPrice: 4299,
    progress: 33
  },
  {
    id: 'sk5',
    name: 'AirPods Pro 2代',
    image: '/images/products/airpods.jpg',
    originalPrice: 1999,
    seckillPrice: 1599,
    progress: 89
  }
])

// 推荐商品数据
const recommendProducts = ref<Product[]>([
  {
    id: 'rec1',
    name: '华为Mate 60 Pro 12GB+256GB',
    image: '/images/products/huawei.jpg',
    price: 6999,
    tag: '新品',
    rating: 5,
    reviewCount: 28000
  },
  {
    id: 'rec2',
    name: '戴森V15 Detect无线吸尘器',
    image: '/images/products/dyson.jpg',
    price: 4590,
    rating: 4,
    reviewCount: 15000
  },
  {
    id: 'rec3',
    name: '海尔冰箱 三开门 528L',
    image: '/images/products/haier.jpg',
    price: 3999,
    tag: '爆款',
    rating: 4,
    reviewCount: 8900
  },
  {
    id: 'rec4',
    name: '美的空调 1.5匹 变频',
    image: '/images/products/midea.jpg',
    price: 2599,
    rating: 4,
    reviewCount: 12000
  },
  {
    id: 'rec5',
    name: '索尼WH-1000XM5头戴式耳机',
    image: '/images/products/sony.jpg',
    price: 2399,
    rating: 5,
    reviewCount: 6800
  },
  {
    id: 'rec6',
    name: '小米电视 65英寸 4K',
    image: '/images/products/mi-tv.jpg',
    price: 2999,
    tag: '热销',
    rating: 4,
    reviewCount: 22000
  }
])

let seckillTimer: NodeJS.Timeout | null = null

// 秒杀倒计时更新
const updateSeckillTime = () => {
  const now = new Date()
  const nextHour = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours() + 1, 0, 0, 0)
  const diff = nextHour.getTime() - now.getTime()
  
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
  const seconds = Math.floor((diff % (1000 * 60)) / 1000)
  
  seckillTime.value = {
    hours: hours.toString().padStart(2, '0'),
    minutes: minutes.toString().padStart(2, '0'),
    seconds: seconds.toString().padStart(2, '0')
  }
}

// 切换推荐标签
const switchTab = (tabId: string) => {
  currentTab.value = tabId
  // 这里可以加载对应标签的数据
  console.log('切换到标签:', tabId)
}

// 查看商品详情
const viewProduct = (product: Product) => {
  router.push(`/product/${product.id}`)
}

onMounted(() => {
  // 开始秒杀倒计时
  updateSeckillTime()
  seckillTimer = setInterval(updateSeckillTime, 1000)
})

onUnmounted(() => {
  if (seckillTimer) {
    clearInterval(seckillTimer)
  }
})
</script>

<style scoped>
.home {
  min-height: 100vh;
  background: #f5f5f5;
}

.main-content {
  padding: 20px 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 首页横幅区域 */
.hero-section {
  margin-bottom: 30px;
}

.hero-content {
  display: grid;
  grid-template-columns: 200px 1fr 200px;
  gap: 20px;
  align-items: flex-start;
}

.category-sidebar {
  position: sticky;
  top: 20px;
}

.carousel-area {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.shortcuts-sidebar {
  position: sticky;
  top: 20px;
}

/* 区域标题 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding: 0 20px;
  height: 60px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.seckill-icon {
  width: 24px;
  height: 24px;
  background: url('/images/seckill-icon.png') no-repeat center;
  background-size: contain;
}

.more-link {
  color: #666;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.more-link:hover {
  color: #e3393c;
}

/* 秒杀区域 */
.seckill-section {
  margin-bottom: 30px;
}

.seckill-timer {
  display: flex;
  align-items: center;
  gap: 10px;
}

.timer-label {
  font-size: 14px;
  color: #666;
}

.timer-display {
  display: flex;
  align-items: center;
  gap: 2px;
}

.timer-item {
  background: #e3393c;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: bold;
  min-width: 24px;
  text-align: center;
}

.timer-separator {
  color: #e3393c;
  font-weight: bold;
  font-size: 16px;
}

.seckill-products {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.seckill-product {
  cursor: pointer;
  transition: transform 0.3s;
}

.seckill-product:hover {
  transform: translateY(-2px);
}

.product-image {
  position: relative;
  margin-bottom: 10px;
}

.product-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 4px;
}

.product-badge {
  position: absolute;
  top: 8px;
  left: 8px;
  background: #e3393c;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
}

.product-info {
  padding: 0 8px;
}

.product-name {
  font-size: 14px;
  color: #333;
  margin: 0 0 8px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.price-current {
  color: #e3393c;
  font-size: 18px;
  font-weight: bold;
}

.price-original {
  color: #999;
  font-size: 14px;
  text-decoration: line-through;
}

.product-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #e3393c;
  transition: width 0.3s;
}

.progress-text {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
}

/* 推荐区域 */
.recommend-section {
  margin-bottom: 30px;
}

.recommend-tabs {
  display: flex;
  gap: 20px;
}

.tab-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: color 0.3s;
}

.tab-btn:hover,
.tab-btn.active {
  color: #e3393c;
}

.recommend-products {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.recommend-product {
  cursor: pointer;
  transition: transform 0.3s;
}

.recommend-product:hover {
  transform: translateY(-2px);
}

.recommend-product .product-image img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 4px;
}

.price-tag {
  background: #ff6b00;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 10px;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 8px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  width: 12px;
  height: 12px;
  background: url('/images/star-empty.png') no-repeat center;
  background-size: contain;
}

.star.filled {
  background-image: url('/images/star-filled.png');
}

.rating-text {
  font-size: 12px;
  color: #666;
}

/* 底部信息 */
.footer {
  background: #fff;
  border-top: 1px solid #e5e5e5;
  padding: 40px 0 20px;
  margin-top: 40px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
  margin-bottom: 20px;
}

.footer-section h4 {
  color: #333;
  margin-bottom: 15px;
  font-size: 16px;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section li {
  margin-bottom: 8px;
}

.footer-section a {
  color: #666;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s;
}

.footer-section a:hover {
  color: #e3393c;
}

.footer-bottom {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e5e5e5;
  color: #999;
  font-size: 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .hero-content {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .category-sidebar,
  .shortcuts-sidebar {
    position: static;
  }
  
  .seckill-products,
  .recommend-products {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
  
  .main-content {
    padding: 15px 0;
  }
  
  .section-header {
    flex-direction: column;
    height: auto;
    padding: 15px;
    gap: 10px;
  }
  
  .seckill-timer {
    order: -1;
  }
  
  .seckill-products,
  .recommend-products {
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    padding: 15px;
  }
  
  .product-image img {
    height: 150px;
  }
  
  .footer-content {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .hero-content {
    gap: 15px;
  }
  
  .seckill-products,
  .recommend-products {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
  }
}
</style>