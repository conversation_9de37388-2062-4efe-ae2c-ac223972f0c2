/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Carousel: typeof import('./src/components/carousel/Carousel.vue')['default']
    CategoryMenu: typeof import('./src/components/menu/CategoryMenu.vue')['default']
    ElBadge: typeof import('element-plus/es')['ElBadge']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElSkeleton: typeof import('element-plus/es')['ElSkeleton']
    ErrorBoundary: typeof import('./src/components/ErrorBoundary.vue')['default']
    HelloWorld: typeof import('./src/components/HelloWorld.vue')['default']
    LoadingSkeleton: typeof import('./src/components/LoadingSkeleton.vue')['default']
    MainNav: typeof import('./src/components/nav/MainNav.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ShortcutMenu: typeof import('./src/components/shortcuts/ShortcutMenu.vue')['default']
    TopNav: typeof import('./src/components/nav/TopNav.vue')['default']
  }
}
